module.exports = {
  apps: [
    {
      name: 'pdfzone-pro',
      script: 'dist/index.js',
      cwd: '/home/<USER>/htdocs/pdfzone.pro',
      instances: 1,
      exec_mode: 'fork',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        PORT: 5001,
        HOST: '0.0.0.0',
        USE_SQLITE: 'true',
        STORAGE_TYPE: 'sqlite',
        BASE_URL: 'https://pdfzone.pro',
        DOMAIN: 'pdfzone.pro'
      },
      
      // Logging
      log_file: '/home/<USER>/htdocs/pdfzone.pro/logs/combined.log',
      out_file: '/home/<USER>/htdocs/pdfzone.pro/logs/out.log',
      error_file: '/home/<USER>/htdocs/pdfzone.pro/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // Advanced settings
      node_args: '--max-old-space-size=1024',
      kill_timeout: 5000,
      listen_timeout: 8000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Source map support
      source_map_support: true,
      
      // Merge logs
      merge_logs: true,
      
      // Time zone
      time: true
    }
  ],
  
  deploy: {
    production: {
      user: 'pdfzone',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: '**************:yourusername/pdftools-pro.git',
      path: '/home/<USER>/htdocs/pdfzone.pro',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.cjs --env production',
      'pre-setup': ''
    }
  }
};
