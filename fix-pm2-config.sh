#!/bin/bash

# Quick fix for PM2 configuration issue
# Run this script on your server to fix the ecosystem config

echo "🔧 Fixing PM2 configuration..."

# Create the correct ecosystem.config.cjs file
cat > ecosystem.config.cjs << 'EOF'
module.exports = {
  apps: [
    {
      name: 'pdfzone-pro',
      script: 'dist/index.js',
      cwd: '/home/<USER>/htdocs/pdfzone.pro',
      instances: 1,
      exec_mode: 'fork',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        PORT: 5001,
        HOST: '0.0.0.0',
        USE_SQLITE: 'true',
        STORAGE_TYPE: 'sqlite',
        BASE_URL: 'https://pdfzone.pro',
        DOMAIN: 'pdfzone.pro'
      },
      
      // Logging
      log_file: '/home/<USER>/htdocs/pdfzone.pro/logs/combined.log',
      out_file: '/home/<USER>/htdocs/pdfzone.pro/logs/out.log',
      error_file: '/home/<USER>/htdocs/pdfzone.pro/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // Advanced settings
      node_args: '--max-old-space-size=1024',
      kill_timeout: 5000,
      listen_timeout: 8000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Source map support
      source_map_support: true,
      
      // Merge logs
      merge_logs: true,
      
      // Time zone
      time: true
    }
  ]
};
EOF

echo "✅ Created ecosystem.config.cjs"

# Remove the old .js file if it exists
if [ -f "ecosystem.config.js" ]; then
    rm ecosystem.config.js
    echo "✅ Removed old ecosystem.config.js"
fi

# Start the application with PM2
echo "🚀 Starting application with PM2..."
pm2 start ecosystem.config.cjs --env production

# Save PM2 configuration
echo "💾 Saving PM2 configuration..."
pm2 save

# Setup PM2 startup script
echo "🔄 Setting up PM2 startup script..."
pm2 startup systemd -u pdfzone --hp /home/<USER>

echo ""
echo "✅ PM2 configuration fixed and application started!"
echo ""
echo "Check status with: pm2 status"
echo "View logs with: pm2 logs pdfzone-pro"
