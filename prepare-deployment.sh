#!/bin/bash

# PDFTools Pro - Deployment Package Preparation Script
# This script prepares the deployment package for CloudPanel.io

set -e  # Exit on any error

echo "📦 Preparing PDFTools Pro deployment package..."
echo "=================================================="

# Configuration
DEPLOYMENT_DIR="pdfzone-pro-deployment"
CURRENT_DIR=$(pwd)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Clean up previous deployment directory
if [ -d "$DEPLOYMENT_DIR" ]; then
    print_status "Removing existing deployment directory..."
    rm -rf "$DEPLOYMENT_DIR"
fi

# Create deployment directory
print_status "Creating deployment directory..."
mkdir -p "$DEPLOYMENT_DIR"

# Build the application
print_status "Building application for production..."
npm run build

if [ ! -f "dist/index.js" ]; then
    print_error "Build failed! dist/index.js not found."
    exit 1
fi

print_success "Application built successfully"

# Copy essential files
print_status "Copying application files..."

# Copy built application
cp -r dist/ "$DEPLOYMENT_DIR/"

# Copy package files
cp package.json "$DEPLOYMENT_DIR/"
cp package-lock.json "$DEPLOYMENT_DIR/"

# Copy configuration files
cp ecosystem.config.js "$DEPLOYMENT_DIR/"
cp .env.production "$DEPLOYMENT_DIR/"

# Copy deployment scripts and documentation
cp deploy.sh "$DEPLOYMENT_DIR/"
cp DEPLOYMENT_GUIDE.md "$DEPLOYMENT_DIR/"

# Copy database and data
print_status "Copying database and data files..."
cp storage.db "$DEPLOYMENT_DIR/"
[ -f storage.db-shm ] && cp storage.db-shm "$DEPLOYMENT_DIR/" || true
[ -f storage.db-wal ] && cp storage.db-wal "$DEPLOYMENT_DIR/" || true

# Copy uploads directory (preserve structure but exclude temp files)
print_status "Copying uploads directory..."
mkdir -p "$DEPLOYMENT_DIR/uploads/temp"
if [ -d "uploads" ]; then
    # Copy uploads but exclude temporary files
    rsync -av --exclude='temp/*' uploads/ "$DEPLOYMENT_DIR/uploads/" || cp -r uploads/ "$DEPLOYMENT_DIR/"
fi

# Install production dependencies in deployment directory
print_status "Installing production dependencies..."
cd "$DEPLOYMENT_DIR"
npm ci --only=production --silent

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p backups

# Set proper permissions
print_status "Setting file permissions..."
chmod +x deploy.sh
chmod +x dist/index.js
chmod 600 .env.production

# Go back to original directory
cd "$CURRENT_DIR"

# Create deployment archive
print_status "Creating deployment archive..."
tar -czf "${DEPLOYMENT_DIR}.tar.gz" "$DEPLOYMENT_DIR"

# Calculate sizes
DEPLOYMENT_SIZE=$(du -sh "$DEPLOYMENT_DIR" | cut -f1)
ARCHIVE_SIZE=$(du -sh "${DEPLOYMENT_DIR}.tar.gz" | cut -f1)

# Display summary
echo ""
echo "=================================================="
print_success "🎉 Deployment package prepared successfully!"
echo "=================================================="
echo ""
echo "📋 Package Information:"
echo "   • Directory: $DEPLOYMENT_DIR ($DEPLOYMENT_SIZE)"
echo "   • Archive: ${DEPLOYMENT_DIR}.tar.gz ($ARCHIVE_SIZE)"
echo "   • Database: SQLite with all existing data"
echo "   • Dependencies: Production-only packages"
echo ""
echo "📁 Package Contents:"
echo "   • dist/ - Built application"
echo "   • node_modules/ - Production dependencies"
echo "   • uploads/ - File upload directory"
echo "   • storage.db - SQLite database"
echo "   • package.json - Dependencies configuration"
echo "   • ecosystem.config.js - PM2 configuration"
echo "   • .env.production - Production environment"
echo "   • deploy.sh - Deployment script"
echo "   • DEPLOYMENT_GUIDE.md - Deployment instructions"
echo ""
echo "🚀 Next Steps:"
echo "   1. Upload $DEPLOYMENT_DIR/ to your server:"
echo "      scp -r $DEPLOYMENT_DIR/* pdfzone@your-server:/home/<USER>/htdocs/pdfzone.pro/"
echo ""
echo "   2. Or upload the archive and extract:"
echo "      scp ${DEPLOYMENT_DIR}.tar.gz pdfzone@your-server:/home/<USER>/"
echo "      ssh pdfzone@your-server"
echo "      cd /home/<USER>"
echo "      tar -xzf ${DEPLOYMENT_DIR}.tar.gz"
echo "      mv ${DEPLOYMENT_DIR}/* htdocs/pdfzone.pro/"
echo ""
echo "   3. Run deployment script on server:"
echo "      cd /home/<USER>/htdocs/pdfzone.pro"
echo "      chmod +x deploy.sh"
echo "      ./deploy.sh"
echo ""
echo "📖 For detailed instructions, see DEPLOYMENT_GUIDE.md"
echo ""
print_success "Deployment package ready! 📦"
