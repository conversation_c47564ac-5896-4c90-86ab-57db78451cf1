# 📋 PDFTools Pro - Deployment Checklist

## ✅ Pre-Deployment Verification

### Local Setup ✅ COMPLETED
- [x] Project configured to use SQLite database
- [x] All existing data preserved and migrated to SQLite
- [x] Production build created and tested
- [x] All dependencies included in deployment package
- [x] Fixed pdf-parse module debug mode issue
- [x] Application tested locally on port 5001

### Deployment Package ✅ READY
- [x] `pdfzone-pro-deployment/` folder created
- [x] `pdfzone-pro-deployment.tar.gz` archive created
- [x] All necessary files included:
  - [x] Built application (`dist/`)
  - [x] Production dependencies (`node_modules/`)
  - [x] SQLite database with data (`storage.db`)
  - [x] PM2 configuration (`ecosystem.config.js`)
  - [x] Environment configuration (`.env.production`)
  - [x] Deployment script (`deploy.sh`)
  - [x] Documentation (`DEPLOYMENT_GUIDE.md`)

## 🚀 Deployment Steps

### Step 1: Upload to Server
- [ ] Upload deployment package to server:
  ```bash
  scp -r pdfzone-pro-deployment/* pdfzone@your-server:/home/<USER>/htdocs/pdfzone.pro/
  ```
  OR
  ```bash
  scp pdfzone-pro-deployment.tar.gz pdfzone@your-server:/home/<USER>/
  ssh pdfzone@your-server
  cd /home/<USER>
  tar -xzf pdfzone-pro-deployment.tar.gz
  mv pdfzone-pro-deployment/* htdocs/pdfzone.pro/
  ```

### Step 2: Run Deployment Script
- [ ] Connect to server:
  ```bash
  ssh pdfzone@your-server
  cd /home/<USER>/htdocs/pdfzone.pro
  ```
- [ ] Make script executable and run:
  ```bash
  chmod +x deploy.sh
  ./deploy.sh
  ```

### Step 3: Verify Deployment
- [ ] Check PM2 status:
  ```bash
  pm2 status
  ```
- [ ] Check application logs:
  ```bash
  pm2 logs pdfzone-pro
  ```
- [ ] Test local connection:
  ```bash
  curl http://localhost:5001
  ```
- [ ] Access via domain:
  ```bash
  curl https://pdfzone.pro
  ```

## ⚙️ Post-Deployment Configuration

### Required Environment Updates
- [ ] Update SESSION_SECRET in `/home/<USER>/htdocs/pdfzone.pro/.env`
- [ ] Update JWT_SECRET in `.env`
- [ ] Configure SMTP settings:
  - [ ] SMTP_USER
  - [ ] SMTP_PASS
- [ ] Configure payment gateways:
  - [ ] STRIPE_SECRET_KEY
  - [ ] STRIPE_PUBLISHABLE_KEY
  - [ ] PAYPAL_CLIENT_ID
  - [ ] PAYPAL_CLIENT_SECRET

### Security Configuration
- [ ] Change default admin password
- [ ] Verify file permissions:
  ```bash
  ls -la storage.db
  ls -la .env
  ```
- [ ] Test SSL certificate (CloudPanel handles this automatically)

## 🧪 Testing Checklist

### Basic Functionality
- [ ] Application loads at https://pdfzone.pro
- [ ] User registration works
- [ ] User login works
- [ ] File upload functionality works
- [ ] PDF processing tools work
- [ ] Database operations work (create, read, update, delete)

### Advanced Features
- [ ] Payment processing (if configured)
- [ ] Email notifications (if SMTP configured)
- [ ] Admin panel access
- [ ] User dashboard functionality
- [ ] File download functionality

## 🔧 Management Commands Reference

### PM2 Process Management
```bash
pm2 status                    # Check application status
pm2 restart pdfzone-pro      # Restart application
pm2 stop pdfzone-pro         # Stop application
pm2 logs pdfzone-pro         # View logs
pm2 monit                    # Monitor resources
pm2 flush pdfzone-pro        # Clear logs
```

### Database Management
```bash
# Backup database
cp storage.db backups/storage-$(date +%Y%m%d-%H%M%S).db

# Check database
sqlite3 storage.db ".tables"
sqlite3 storage.db "SELECT COUNT(*) FROM users;"
```

### Log Management
```bash
# View logs
tail -f logs/combined.log
tail -f logs/error.log
tail -f logs/out.log
```

## 🚨 Troubleshooting

### Common Issues
- [ ] **Port 5001 not accessible**: Check firewall settings
- [ ] **Database errors**: Verify storage.db permissions
- [ ] **Application won't start**: Check PM2 logs
- [ ] **SSL issues**: Verify CloudPanel SSL configuration

### Debug Commands
```bash
# Check port usage
netstat -tlnp | grep 5001

# Check file permissions
ls -la storage.db
ls -la dist/index.js

# Check Node.js version
node --version

# Check PM2 version
pm2 --version
```

## 📞 Support Information

### Log Locations
- Application logs: `/home/<USER>/htdocs/pdfzone.pro/logs/`
- PM2 logs: `~/.pm2/logs/`
- System logs: `/var/log/`

### Important Files
- Application: `/home/<USER>/htdocs/pdfzone.pro/dist/index.js`
- Database: `/home/<USER>/htdocs/pdfzone.pro/storage.db`
- Configuration: `/home/<USER>/htdocs/pdfzone.pro/.env`
- PM2 Config: `/home/<USER>/htdocs/pdfzone.pro/ecosystem.config.js`

## ✅ Success Criteria

Deployment is successful when:
- [x] PM2 shows "pdfzone-pro" as "online"
- [x] Application responds on https://pdfzone.pro
- [x] All existing data is accessible
- [x] File uploads work correctly
- [x] User authentication functions
- [x] PDF processing tools work
- [x] No errors in PM2 logs

---

**🎉 Once all items are checked, your PDFTools Pro deployment is complete!**
