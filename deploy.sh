#!/bin/bash

# PDFTools Pro Deployment Script for CloudPanel.io
# This script should be run on the server after uploading the deployment package

set -e  # Exit on any error

echo "🚀 Starting PDFTools Pro deployment..."
echo "=================================================="

# Configuration
APP_NAME="pdfzone-pro"
APP_DIR="/home/<USER>/htdocs/pdfzone.pro"
USER="pdfzone"
PORT="5001"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as correct user
if [ "$USER" != "pdfzone" ]; then
    print_warning "Not running as pdfzone user. Switching to pdfzone user..."
    sudo -u pdfzone bash "$0" "$@"
    exit $?
fi

# Navigate to application directory
print_status "Navigating to application directory: $APP_DIR"
cd "$APP_DIR" || {
    print_error "Failed to navigate to $APP_DIR"
    exit 1
}

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p uploads/temp
mkdir -p backups

# Set proper permissions
print_status "Setting file permissions..."
chmod 755 "$APP_DIR"
chmod 755 dist/
chmod +x dist/index.js
chmod 644 package.json
chmod 644 ecosystem.config.js
chmod 600 .env.production
chmod 755 uploads/
chmod 755 uploads/temp/
chmod 755 logs/
chmod 644 storage.db
chmod 644 storage.db-shm 2>/dev/null || true
chmod 644 storage.db-wal 2>/dev/null || true

# Install Node.js dependencies (production only)
print_status "Installing Node.js dependencies..."
if [ -f "package.json" ]; then
    npm ci --only=production --silent
    print_success "Dependencies installed successfully"
else
    print_error "package.json not found!"
    exit 1
fi

# Verify database exists
print_status "Verifying SQLite database..."
if [ -f "storage.db" ]; then
    print_success "SQLite database found"
else
    print_error "SQLite database not found! Please ensure storage.db is included in the deployment package."
    exit 1
fi

# Copy production environment file
print_status "Setting up environment configuration..."
if [ -f ".env.production" ]; then
    cp .env.production .env
    print_success "Production environment configured"
else
    print_warning ".env.production not found, using default configuration"
fi

# Stop existing PM2 process if running
print_status "Stopping existing application..."
pm2 stop "$APP_NAME" 2>/dev/null || print_warning "No existing process to stop"
pm2 delete "$APP_NAME" 2>/dev/null || print_warning "No existing process to delete"

# Start application with PM2
print_status "Starting application with PM2..."
pm2 start ecosystem.config.cjs --env production

# Save PM2 configuration
print_status "Saving PM2 configuration..."
pm2 save

# Setup PM2 startup script
print_status "Setting up PM2 startup script..."
pm2 startup systemd -u "$USER" --hp "/home/<USER>" || print_warning "PM2 startup setup may require manual configuration"

# Verify application is running
print_status "Verifying application status..."
sleep 5

if pm2 list | grep -q "$APP_NAME.*online"; then
    print_success "Application is running successfully!"
else
    print_error "Application failed to start. Check logs with: pm2 logs $APP_NAME"
    exit 1
fi

# Display application information
echo ""
echo "=================================================="
print_success "🎉 PDFTools Pro deployment completed successfully!"
echo "=================================================="
echo ""
echo "📋 Application Information:"
echo "   • Name: $APP_NAME"
echo "   • Directory: $APP_DIR"
echo "   • Port: $PORT"
echo "   • Domain: pdfzone.pro"
echo "   • Database: SQLite (storage.db)"
echo ""
echo "🔧 Management Commands:"
echo "   • View logs: pm2 logs $APP_NAME"
echo "   • Restart app: pm2 restart $APP_NAME"
echo "   • Stop app: pm2 stop $APP_NAME"
echo "   • App status: pm2 status"
echo "   • Monitor: pm2 monit"
echo ""
echo "🌐 Access your application at: https://pdfzone.pro"
echo ""
echo "📝 Next Steps:"
echo "   1. Configure your domain DNS to point to this server"
echo "   2. Set up SSL certificate (CloudPanel.io handles this automatically)"
echo "   3. Update SMTP settings in .env file"
echo "   4. Update payment gateway credentials in .env file"
echo "   5. Test all functionality"
echo ""
print_success "Deployment completed! 🚀"
