# Production Environment Configuration for PDFTools Pro
# CloudPanel.io Deployment Configuration

# Application Settings
NODE_ENV=production
PORT=5001
HOST=0.0.0.0

# Database Configuration
USE_SQLITE=true
STORAGE_TYPE=sqlite

# Domain and URL Configuration
BASE_URL=https://pdfzone.pro
DOMAIN=pdfzone.pro

# Session Configuration
SESSION_SECRET=your-super-secure-session-secret-change-this-in-production

# File Upload Configuration
MAX_FILE_SIZE=50
UPLOAD_PATH=/home/<USER>/htdocs/pdfzone.pro/uploads

# Email Configuration (Update with your SMTP settings)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=PDF Zone Pro

# Payment Gateway Configuration (Update with your credentials)
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_ENVIRONMENT=live

# Security Settings
BCRYPT_ROUNDS=12
JWT_SECRET=your-jwt-secret-change-this-in-production

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=/home/<USER>/htdocs/pdfzone.pro/logs/app.log

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Feature Flags
REGISTRATION_ENABLED=true
MAINTENANCE_MODE=false
